# Mac安装检查清单

## ✅ 已完成的检查项目

### 1. 系统环境
- ✅ Node.js 已安装并可用
- ✅ npm 已安装并可用
- ✅ mcp-chrome-bridge 已全局安装

### 2. 项目构建
- ✅ 项目依赖已安装 (node_modules存在)
- ✅ TypeScript已编译 (dist/stdio-server.js存在)
- ✅ stdio MCP服务器功能正常

### 3. Chrome扩展
- ✅ manifest.json 存在
- ✅ background.js 存在
- ✅ popup.html 存在
- ✅ 扩展ID已正确计算: `jcwgqphqhmrdkeiq`

### 4. Native Messaging配置
- ✅ Native messaging host已注册
- ✅ 配置文件位置正确: `/Users/<USER>/Library/Application Support/Google/Chrome/NativeMessagingHosts/com.chromemcp.nativehost.json`
- ✅ 扩展ID已修复匹配: `chrome-extension://jcwgqphqhmrdkeiq/`

### 5. Augment配置
- ✅ .vscode/mcp.json 已创建
- ✅ 配置指向正确的stdio服务器路径

## 🚀 安装完成 - 可以安全删除并重新导入Chrome扩展

### 重新导入Chrome扩展的步骤：

1. **删除现有扩展**：
   - 打开Chrome，访问 `chrome://extensions/`
   - 找到现有的chrome-mcp-server扩展
   - 点击"移除"删除

2. **重新加载扩展**：
   - 确保"开发者模式"已启用
   - 点击"加载已解压的扩展程序"
   - 选择目录: `/Users/<USER>/Documents/mcp/chrome-mcp-server-0.0.3`
   - 扩展应该以ID `jcwgqphqhmrdkeiq` 加载

3. **验证扩展**：
   - 扩展应该显示为已启用
   - 点击扩展图标应该显示popup界面
   - 在popup中应该能看到"Native 连接状态"

### Augment配置：

在Augment的MCP设置中添加：

```json
{
  "servers": {
    "chrome-mcp-server": {
      "type": "stdio",
      "command": "node",
      "args": ["/Users/<USER>/Documents/mcp/chrome-mcp-server-0.0.3/dist/stdio-server.js"]
    }
  }
}
```

### 测试连接：

重新导入扩展后，可以运行以下命令测试：

```bash
cd /Users/<USER>/Documents/mcp/chrome-mcp-server-0.0.3
node test-stdio.js
```

## 🔧 故障排除

如果遇到问题：

1. **Native连接失败**：
   ```bash
   node fix-extension-id.js
   ```

2. **重新注册native messaging host**：
   ```bash
   mcp-chrome-bridge register
   ```

3. **完整验证**：
   ```bash
   node verify-installation.js
   ```

## ✨ 确认状态

- ✅ 所有组件已正确安装
- ✅ 配置文件已正确生成
- ✅ 扩展ID问题已修复
- ✅ stdio MCP服务器功能正常
- ✅ 可以安全删除并重新导入Chrome扩展

**结论：项目已准备就绪，可以删除Chrome扩展并重新导入！**
