#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function updateDebugConfig() {
  console.log('🔧 Updating native messaging config for debugging...\n');
  
  const hostConfig = {
    name: "com.chromemcp.nativehost",
    description: "Chrome MCP Server Native Host (Debug)",
    path: path.resolve(__dirname, "debug-native-host.sh"),
    type: "stdio",
    allowed_origins: [
      "chrome-extension://hbdgbgagpkpjffpklnamcljpakneikee/"
    ]
  };
  
  const configDir = path.join(process.env.HOME, 'Library/Application Support/Google/Chrome/NativeMessagingHosts');
  const configPath = path.join(configDir, 'com.chromemcp.nativehost.json');
  
  fs.writeFileSync(configPath, JSON.stringify(hostConfig, null, 2));
  console.log(`✅ Updated debug configuration: ${configPath}`);
  
  console.log('\n📋 Debug configuration:');
  console.log(JSON.stringify(hostConfig, null, 2));
  
  console.log('\n📝 Debug log will be written to: /tmp/chrome-mcp-debug.log');
  console.log('You can monitor it with: tail -f /tmp/chrome-mcp-debug.log');
}

if (require.main === module) {
  updateDebugConfig();
}
