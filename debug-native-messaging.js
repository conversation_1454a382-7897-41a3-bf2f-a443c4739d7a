#!/usr/bin/env node

/**
 * Debug native messaging setup
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

function debugNativeMessaging() {
  console.log('🔍 Debugging native messaging setup...\n');
  
  // Check if native host script exists and is executable
  const hostScript = path.resolve(__dirname, 'run_native_host.sh');
  console.log(`📋 Native host script: ${hostScript}`);
  
  if (fs.existsSync(hostScript)) {
    const stats = fs.statSync(hostScript);
    console.log(`✅ Script exists, permissions: ${stats.mode.toString(8)}`);
    
    // Check script content
    const content = fs.readFileSync(hostScript, 'utf8');
    console.log('📋 Script content:');
    console.log(content);
  } else {
    console.log('❌ Script does not exist');
    return;
  }
  
  // Check native host configuration
  const configPath = path.join(process.env.HOME, 'Library/Application Support/Google/Chrome/NativeMessagingHosts/com.chromemcp.nativehost.json');
  console.log(`\n📋 Native host config: ${configPath}`);
  
  if (fs.existsSync(configPath)) {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    console.log('✅ Config exists:');
    console.log(JSON.stringify(config, null, 2));
  } else {
    console.log('❌ Config does not exist');
    return;
  }
  
  // Test if stdio server can start
  console.log('\n🚀 Testing stdio server startup...');
  const serverPath = path.resolve(__dirname, 'dist/stdio-server.js');
  
  if (fs.existsSync(serverPath)) {
    console.log('✅ stdio-server.js exists');
    
    // Test server with a simple message
    console.log('📤 Testing server with ping message...');
    
    const server = spawn('node', [serverPath], {
      stdio: ['pipe', 'pipe', 'inherit'],
      cwd: __dirname
    });
    
    let output = '';
    server.stdout.on('data', (data) => {
      output += data.toString();
      console.log('📨 Server output:', data.toString().trim());
    });
    
    server.on('error', (error) => {
      console.log('❌ Server error:', error.message);
    });
    
    server.on('exit', (code) => {
      console.log(`📊 Server exited with code: ${code}`);
    });
    
    // Send a test message
    const testMessage = JSON.stringify({
      jsonrpc: "2.0",
      method: "tools/list",
      id: 1
    }) + '\n';
    
    server.stdin.write(testMessage);
    
    // Close after 3 seconds
    setTimeout(() => {
      server.kill('SIGTERM');
    }, 3000);
    
  } else {
    console.log('❌ stdio-server.js does not exist');
  }
}

if (require.main === module) {
  debugNativeMessaging();
}
