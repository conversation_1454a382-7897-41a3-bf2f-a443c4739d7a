#!/bin/bash

# Debug version of native host script
LOG_FILE="/tmp/chrome-mcp-debug.log"

echo "$(date): Native host script started" >> "$LOG_FILE"
echo "$(date): Working directory: $(pwd)" >> "$LOG_FILE"
echo "$(date): Arguments: $@" >> "$LOG_FILE"
echo "$(date): Environment variables:" >> "$LOG_FILE"
env >> "$LOG_FILE"

cd "/Users/<USER>/Documents/mcp/chrome-mcp-server-0.0.3"
echo "$(date): Changed to directory: $(pwd)" >> "$LOG_FILE"

if [ ! -f "dist/stdio-server.js" ]; then
    echo "$(date): ERROR: stdio-server.js not found" >> "$LOG_FILE"
    exit 1
fi

echo "$(date): Starting stdio-server.js" >> "$LOG_FILE"
exec node dist/stdio-server.js 2>> "$LOG_FILE"
