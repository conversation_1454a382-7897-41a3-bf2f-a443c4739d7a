#!/usr/bin/env node

/**
 * Update native messaging host configuration to point to local project
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

function updateNativeHostConfig() {
  console.log('🔧 Updating native messaging host configuration...\n');
  
  // Calculate extension ID from manifest
  const manifest = JSON.parse(fs.readFileSync('manifest.json', 'utf8'));
  const keyBase64 = manifest.key;
  const keyBuffer = Buffer.from(keyBase64, 'base64');
  const hash = crypto.createHash('sha256').update(keyBuffer).digest();
  const extensionId = Array.from(hash.slice(0, 16))
    .map(byte => String.fromCharCode(97 + (byte % 26)))
    .join('');
  
  console.log(`📋 Extension ID: ${extensionId}`);
  
  // Create native host configuration
  const hostConfig = {
    name: "com.chromemcp.nativehost",
    description: "Chrome MCP Server Native Host",
    path: path.resolve(__dirname, "run_native_host.sh"),
    type: "stdio",
    allowed_origins: [`chrome-extension://${extensionId}/`]
  };
  
  // Ensure the native host script exists and is executable
  const nativeHostScript = path.resolve(__dirname, "run_native_host.sh");
  const nativeHostContent = `#!/bin/bash
cd "${__dirname}"
exec node dist/stdio-server.js
`;
  
  fs.writeFileSync(nativeHostScript, nativeHostContent);
  fs.chmodSync(nativeHostScript, '755');
  console.log(`✅ Created native host script: ${nativeHostScript}`);
  
  // Write native host configuration
  const configDir = path.join(process.env.HOME, 'Library/Application Support/Google/Chrome/NativeMessagingHosts');
  const configPath = path.join(configDir, 'com.chromemcp.nativehost.json');
  
  // Ensure directory exists
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
  }
  
  fs.writeFileSync(configPath, JSON.stringify(hostConfig, null, 2));
  console.log(`✅ Updated native host configuration: ${configPath}`);
  
  console.log('\n📋 New configuration:');
  console.log(JSON.stringify(hostConfig, null, 2));
  
  console.log('\n🎉 Native messaging host configuration updated successfully!');
  console.log('\nNext steps:');
  console.log('1. Reload the Chrome extension');
  console.log('2. Test the connection in the extension popup');
}

if (require.main === module) {
  updateNativeHostConfig();
}
