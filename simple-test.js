#!/usr/bin/env node

/**
 * Extremely simple native messaging test
 * Just logs that it started and stays alive
 */

const fs = require('fs');

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp}: ${message}\n`;
  fs.appendFileSync('/tmp/chrome-mcp-simple.log', logMessage);
}

log('=== SIMPLE TEST STARTED ===');
log(`Process ID: ${process.pid}`);
log(`Working directory: ${process.cwd()}`);
log(`Node version: ${process.version}`);
log(`Arguments: ${JSON.stringify(process.argv)}`);

// Log environment variables that might be relevant
const relevantEnvVars = ['PATH', 'NODE_PATH', 'HOME', 'USER'];
relevantEnvVars.forEach(varName => {
  if (process.env[varName]) {
    log(`${varName}: ${process.env[varName]}`);
  }
});

// Set up stdin to be non-blocking
process.stdin.setEncoding('utf8');

process.stdin.on('readable', () => {
  log('stdin readable event triggered');
  const chunk = process.stdin.read();
  if (chunk !== null) {
    log(`Received data: ${JSON.stringify(chunk)}`);
  }
});

process.stdin.on('end', () => {
  log('stdin ended');
  process.exit(0);
});

process.stdin.on('error', (error) => {
  log(`stdin error: ${error.message}`);
  process.exit(1);
});

// Handle process signals
process.on('SIGINT', () => {
  log('Received SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('Received SIGTERM');
  process.exit(0);
});

process.on('exit', (code) => {
  log(`Process exiting with code: ${code}`);
});

process.on('uncaughtException', (error) => {
  log(`Uncaught exception: ${error.message}`);
  log(`Stack: ${error.stack}`);
  process.exit(1);
});

// Keep the process alive and log periodically
let counter = 0;
const interval = setInterval(() => {
  counter++;
  log(`Heartbeat ${counter} - process still alive`);
  
  // Exit after 60 seconds to prevent runaway processes
  if (counter >= 12) {
    log('Timeout reached, exiting');
    clearInterval(interval);
    process.exit(0);
  }
}, 5000);

log('=== SIMPLE TEST SETUP COMPLETE ===');
