#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function setupUniversalConfig() {
  console.log('🌐 Setting up universal native messaging config...\n');
  
  const hostConfig = {
    name: "com.chromemcp.nativehost",
    description: "Chrome MCP Server Native Host (Universal Test)",
    path: path.resolve(__dirname, "test-native-messaging.js"),
    type: "stdio",
    allowed_origins: [
      "chrome-extension://hbdgbgagpkpjffpklnamcljpakneikee/",
      "chrome-extension://*/",  // Allow any extension for testing
    ]
  };
  
  const configDir = path.join(process.env.HOME, 'Library/Application Support/Google/Chrome/NativeMessagingHosts');
  const configPath = path.join(configDir, 'com.chromemcp.nativehost.json');
  
  fs.writeFileSync(configPath, JSON.stringify(hostConfig, null, 2));
  console.log(`✅ Updated universal configuration: ${configPath}`);
  
  console.log('\n📋 Universal configuration:');
  console.log(JSON.stringify(hostConfig, null, 2));
  
  console.log('\n📝 Test log will be written to: /tmp/chrome-mcp-test.log');
  
  // Clear old logs
  try {
    fs.unlinkSync('/tmp/chrome-mcp-test.log');
    console.log('🗑️ Cleared old test log');
  } catch (e) {
    console.log('📝 No old test log to clear');
  }
  
  console.log('\n🔄 Please restart Chrome completely and try again');
  console.log('💡 You can also check chrome://extensions/ to verify the extension ID');
}

if (require.main === module) {
  setupUniversalConfig();
}
