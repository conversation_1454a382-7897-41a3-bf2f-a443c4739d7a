#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function setupSimpleConfig() {
  console.log('🔧 Setting up simple test native messaging config...\n');
  
  const hostConfig = {
    name: "com.chromemcp.nativehost",
    description: "Chrome MCP Server Native Host (Simple Test)",
    path: path.resolve(__dirname, "simple-test.js"),
    type: "stdio",
    allowed_origins: [
      "chrome-extension://hbdgbgagpkpjffpklnamcljpakneikee/",
      "chrome-extension://*/"
    ]
  };
  
  const configDir = path.join(process.env.HOME, 'Library/Application Support/Google/Chrome/NativeMessagingHosts');
  const configPath = path.join(configDir, 'com.chromemcp.nativehost.json');
  
  fs.writeFileSync(configPath, JSON.stringify(hostConfig, null, 2));
  console.log(`✅ Updated simple test configuration: ${configPath}`);
  
  console.log('\n📋 Simple test configuration:');
  console.log(JSON.stringify(hostConfig, null, 2));
  
  console.log('\n📝 Simple test log will be written to: /tmp/chrome-mcp-simple.log');
  
  // Clear old logs
  try {
    fs.unlinkSync('/tmp/chrome-mcp-simple.log');
    console.log('🗑️ Cleared old simple test log');
  } catch (e) {
    console.log('📝 No old simple test log to clear');
  }
  
  console.log('\n📋 Testing steps:');
  console.log('1. 🔄 Restart Chrome completely');
  console.log('2. 🔌 Open Chrome MCP Server extension');
  console.log('3. 🔗 Click "Connect" button');
  console.log('4. 📊 Monitor log: tail -f /tmp/chrome-mcp-simple.log');
  console.log('5. 🔍 Check if any log entries appear');
}

if (require.main === module) {
  setupSimpleConfig();
}
