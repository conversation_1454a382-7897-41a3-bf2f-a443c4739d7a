#!/usr/bin/env node

/**
 * Test native messaging connection directly
 */

const { spawn } = require('child_process');
const fs = require('fs');

function testNativeHost() {
  console.log('🔍 Testing native messaging host directly...\n');
  
  // Get the native host path
  const hostConfigPath = `${process.env.HOME}/Library/Application Support/Google/Chrome/NativeMessagingHosts/com.chromemcp.nativehost.json`;
  
  if (!fs.existsSync(hostConfigPath)) {
    console.log('❌ Native messaging host configuration not found');
    return;
  }
  
  const config = JSON.parse(fs.readFileSync(hostConfigPath, 'utf8'));
  console.log('📋 Native host configuration:');
  console.log(JSON.stringify(config, null, 2));
  
  const hostPath = config.path;
  console.log(`\n🚀 Starting native host: ${hostPath}`);
  
  // Test the native host directly
  const nativeHost = spawn('bash', [hostPath], {
    stdio: ['pipe', 'pipe', 'inherit']
  });
  
  let responseReceived = false;
  
  nativeHost.stdout.on('data', (data) => {
    console.log('📨 Native host response:', data.toString());
    responseReceived = true;
    nativeHost.kill();
  });
  
  nativeHost.on('error', (error) => {
    console.log('❌ Native host error:', error.message);
  });
  
  nativeHost.on('exit', (code) => {
    console.log(`\n📊 Native host exited with code: ${code}`);
    if (responseReceived) {
      console.log('✅ Native host is working');
    } else {
      console.log('❌ Native host did not respond');
    }
  });
  
  // Send a test message
  const testMessage = {
    type: 'start',
    payload: { port: 12306 }
  };
  
  const messageBuffer = Buffer.from(JSON.stringify(testMessage));
  const lengthBuffer = Buffer.alloc(4);
  lengthBuffer.writeUInt32LE(messageBuffer.length, 0);
  
  console.log('📤 Sending test message to native host...');
  nativeHost.stdin.write(lengthBuffer);
  nativeHost.stdin.write(messageBuffer);
  
  // Timeout after 5 seconds
  setTimeout(() => {
    if (!responseReceived) {
      console.log('⏰ Test timeout');
      nativeHost.kill();
    }
  }, 5000);
}

function checkExtensionId() {
  console.log('🔍 Checking extension ID calculation...\n');
  
  const crypto = require('crypto');
  const fs = require('fs');
  
  try {
    const manifest = JSON.parse(fs.readFileSync('manifest.json', 'utf8'));
    const keyBase64 = manifest.key;
    const keyBuffer = Buffer.from(keyBase64, 'base64');
    
    // Calculate extension ID
    const hash = crypto.createHash('sha256').update(keyBuffer).digest();
    const extensionId = Array.from(hash.slice(0, 16))
      .map(byte => String.fromCharCode(97 + (byte % 26)))
      .join('');
    
    console.log(`📋 Calculated extension ID: ${extensionId}`);
    
    // Check native host config
    const hostConfigPath = `${process.env.HOME}/Library/Application Support/Google/Chrome/NativeMessagingHosts/com.chromemcp.nativehost.json`;
    const config = JSON.parse(fs.readFileSync(hostConfigPath, 'utf8'));
    const expectedOrigin = `chrome-extension://${extensionId}/`;
    
    if (config.allowed_origins.includes(expectedOrigin)) {
      console.log('✅ Extension ID matches in native host config');
    } else {
      console.log('❌ Extension ID mismatch in native host config');
      console.log(`Expected: ${expectedOrigin}`);
      console.log(`Found: ${config.allowed_origins[0]}`);
    }
    
  } catch (error) {
    console.log('❌ Error checking extension ID:', error.message);
  }
}

async function main() {
  console.log('🔧 Chrome MCP Server Native Connection Test\n');
  
  checkExtensionId();
  console.log('\n' + '='.repeat(50) + '\n');
  testNativeHost();
}

if (require.main === module) {
  main().catch(console.error);
}
