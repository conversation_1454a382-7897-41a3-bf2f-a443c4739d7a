#!/bin/bash

echo "🔧 Chrome MCP Server 故障排除..."

echo "1. 检查扩展ID..."
node fix-extension-id.js

echo -e "\n2. 重新注册native messaging host..."
mcp-chrome-bridge register

echo -e "\n3. 验证安装..."
node verify-installation.js

echo -e "\n4. 测试stdio服务器..."
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test","version":"1.0.0"}}}' | timeout 5s node dist/stdio-server.js

echo -e "\n✅ 故障排除完成！"
echo "请重新加载Chrome扩展，然后在Augment中测试连接。"
