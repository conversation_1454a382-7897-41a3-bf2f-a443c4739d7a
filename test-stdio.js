#!/usr/bin/env node

/**
 * Test script to verify the stdio MCP server works correctly
 */

const { spawn } = require('child_process');

async function testStdioServer() {
  console.log('Testing Chrome MCP Server with stdio transport...');
  
  // Start the stdio server
  const server = spawn('node', ['dist/stdio-server.js'], {
    stdio: ['pipe', 'pipe', 'inherit']
  });

  // Test initialize request
  const initializeRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    }
  };

  // Test list tools request
  const listToolsRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list',
    params: {}
  };

  let responseCount = 0;
  let responses = [];
  let buffer = '';

  server.stdout.on('data', (data) => {
    buffer += data.toString();

    // Try to parse complete JSON objects from the buffer
    let startIndex = 0;
    let braceCount = 0;
    let inString = false;
    let escaped = false;

    for (let i = 0; i < buffer.length; i++) {
      const char = buffer[i];

      if (escaped) {
        escaped = false;
        continue;
      }

      if (char === '\\') {
        escaped = true;
        continue;
      }

      if (char === '"') {
        inString = !inString;
        continue;
      }

      if (!inString) {
        if (char === '{') {
          braceCount++;
        } else if (char === '}') {
          braceCount--;

          if (braceCount === 0) {
            // Found a complete JSON object
            const jsonStr = buffer.slice(startIndex, i + 1);
            try {
              const response = JSON.parse(jsonStr);
              responses.push(response);
              responseCount++;

              console.log(`Response ${responseCount}:`, JSON.stringify(response, null, 2));

              if (responseCount >= 2) {
                // We've received both responses, close the server
                server.kill();
                return;
              }
            } catch (error) {
              console.error('Error parsing JSON:', error);
              console.error('JSON string:', jsonStr.substring(0, 200) + '...');
            }

            // Move to next potential JSON object
            startIndex = i + 1;
          }
        }
      }
    }

    // Keep remaining buffer for next data chunk
    buffer = buffer.slice(startIndex);
  });

  server.on('error', (error) => {
    console.error('Server error:', error);
  });

  server.on('exit', (code) => {
    console.log(`Server exited with code ${code}`);
    
    // Analyze responses
    if (responses.length >= 2) {
      console.log('\n=== Test Results ===');
      console.log('✓ Server responded to initialize request');
      console.log('✓ Server responded to list tools request');
      
      const toolsResponse = responses.find(r => r.id === 2);
      if (toolsResponse && toolsResponse.result && toolsResponse.result.tools) {
        console.log(`✓ Found ${toolsResponse.result.tools.length} tools available`);
        console.log('Available tools:', toolsResponse.result.tools.map(t => t.name).join(', '));
      }
      
      console.log('\n✅ stdio MCP server test completed successfully!');
    } else {
      console.log('\n❌ Test failed - not enough responses received');
    }
  });

  // Send test requests
  setTimeout(() => {
    console.log('Sending initialize request...');
    server.stdin.write(JSON.stringify(initializeRequest) + '\n');
  }, 1000);

  setTimeout(() => {
    console.log('Sending list tools request...');
    server.stdin.write(JSON.stringify(listToolsRequest) + '\n');
  }, 2000);

  // Timeout after 10 seconds
  setTimeout(() => {
    console.log('Test timeout, killing server...');
    server.kill();
  }, 10000);
}

testStdioServer().catch(console.error);
