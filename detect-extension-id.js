#!/usr/bin/env node

/**
 * Detect the actual Chrome extension ID and update native messaging configuration
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

function detectExtensionId() {
  console.log('🔍 Detecting Chrome extension ID...\n');
  
  // Method 1: Calculate from manifest key
  try {
    const manifest = JSON.parse(fs.readFileSync('manifest.json', 'utf8'));
    if (manifest.key) {
      const keyBase64 = manifest.key;
      const keyBuffer = Buffer.from(keyBase64, 'base64');
      const hash = crypto.createHash('sha256').update(keyBuffer).digest();
      const calculatedId = Array.from(hash.slice(0, 16))
        .map(byte => String.fromCharCode(97 + (byte % 26)))
        .join('');
      
      console.log(`📋 Calculated ID from manifest key: ${calculatedId}`);
    }
  } catch (error) {
    console.log('❌ Could not calculate ID from manifest key:', error.message);
  }
  
  // Method 2: Check Chrome extensions directory
  const extensionsDir = path.join(process.env.HOME, 'Library/Application Support/Google/Chrome/Default/Extensions');
  
  if (fs.existsSync(extensionsDir)) {
    console.log('\n🔍 Checking Chrome extensions directory...');
    const extensions = fs.readdirSync(extensionsDir);
    
    console.log('\n📋 Found extensions:');
    extensions.forEach(extId => {
      const extPath = path.join(extensionsDir, extId);
      if (fs.statSync(extPath).isDirectory()) {
        try {
          // Look for version directories
          const versions = fs.readdirSync(extPath);
          versions.forEach(version => {
            const versionPath = path.join(extPath, version);
            const manifestPath = path.join(versionPath, 'manifest.json');
            
            if (fs.existsSync(manifestPath)) {
              try {
                const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
                if (manifest.name && manifest.name.toLowerCase().includes('chrome') && 
                    manifest.name.toLowerCase().includes('mcp')) {
                  console.log(`✅ Found Chrome MCP extension: ${extId}`);
                  console.log(`   Name: ${manifest.name}`);
                  console.log(`   Version: ${manifest.version}`);
                  return extId;
                }
              } catch (e) {
                // Ignore invalid manifests
              }
            }
          });
        } catch (e) {
          // Ignore directories we can't read
        }
      }
    });
  }
  
  // Method 3: Look for unpacked extensions (developer mode)
  console.log('\n🔍 Note: If the extension is loaded in developer mode, the ID will be different.');
  console.log('Please check chrome://extensions/ and look for the extension ID there.');
  
  return null;
}

function updateNativeHostWithId(extensionId) {
  console.log(`\n🔧 Updating native messaging host for extension ID: ${extensionId}`);
  
  const hostConfig = {
    name: "com.chromemcp.nativehost",
    description: "Chrome MCP Server Native Host",
    path: path.resolve(__dirname, "run_native_host.sh"),
    type: "stdio",
    allowed_origins: [`chrome-extension://${extensionId}/`]
  };
  
  const configDir = path.join(process.env.HOME, 'Library/Application Support/Google/Chrome/NativeMessagingHosts');
  const configPath = path.join(configDir, 'com.chromemcp.nativehost.json');
  
  fs.writeFileSync(configPath, JSON.stringify(hostConfig, null, 2));
  console.log(`✅ Updated native host configuration: ${configPath}`);
  
  console.log('\n📋 New configuration:');
  console.log(JSON.stringify(hostConfig, null, 2));
}

if (require.main === module) {
  const detectedId = detectExtensionId();
  
  console.log('\n📝 Manual steps:');
  console.log('1. Open chrome://extensions/');
  console.log('2. Find the Chrome MCP Server extension');
  console.log('3. Copy the extension ID (long string of letters)');
  console.log('4. Run: node detect-extension-id.js <extension-id>');
  
  // If extension ID provided as argument
  if (process.argv[2]) {
    const providedId = process.argv[2];
    console.log(`\n🔧 Using provided extension ID: ${providedId}`);
    updateNativeHostWithId(providedId);
  }
}
