#!/usr/bin/env node

/**
 * Fix Chrome extension ID mismatch
 * This script calculates the correct extension ID and updates the native messaging host configuration
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

function calculateExtensionId(publicKeyPem) {
  // Remove PEM headers and decode base64
  const publicKeyBase64 = publicKeyPem
    .replace(/-----BEGIN PUBLIC KEY-----/, '')
    .replace(/-----END PUBLIC KEY-----/, '')
    .replace(/\s/g, '');
  
  const publicKeyBuffer = Buffer.from(publicKeyBase64, 'base64');
  
  // Calculate SHA256 hash
  const hash = crypto.createHash('sha256').update(publicKeyBuffer).digest();
  
  // Take first 16 bytes and convert to extension ID format
  const extensionId = Array.from(hash.slice(0, 16))
    .map(byte => String.fromCharCode(97 + (byte % 26))) // Convert to a-p
    .join('');
  
  return extensionId;
}

function extractPublicKeyFromManifest() {
  try {
    const manifestPath = path.join(__dirname, 'manifest.json');
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    if (!manifest.key) {
      throw new Error('No key found in manifest.json');
    }
    
    // The key in manifest.json is base64 encoded DER format
    // We need to convert it to PEM format first
    const keyBase64 = manifest.key;
    const keyBuffer = Buffer.from(keyBase64, 'base64');
    
    // Convert DER to PEM format
    const pemKey = '-----BEGIN PUBLIC KEY-----\n' +
      keyBuffer.toString('base64').match(/.{1,64}/g).join('\n') +
      '\n-----END PUBLIC KEY-----';
    
    return pemKey;
  } catch (error) {
    console.error('Error reading manifest.json:', error.message);
    return null;
  }
}

function updateNativeMessagingHost(extensionId) {
  const hostConfigPath = path.join(
    process.env.HOME,
    'Library/Application Support/Google/Chrome/NativeMessagingHosts/com.chromemcp.nativehost.json'
  );
  
  try {
    if (!fs.existsSync(hostConfigPath)) {
      console.log('❌ Native messaging host configuration not found');
      console.log('Please run: mcp-chrome-bridge register');
      return false;
    }
    
    const config = JSON.parse(fs.readFileSync(hostConfigPath, 'utf8'));
    const newOrigin = `chrome-extension://${extensionId}/`;
    
    if (config.allowed_origins && config.allowed_origins.includes(newOrigin)) {
      console.log('✅ Extension ID already matches in native messaging host configuration');
      return true;
    }
    
    // Update the configuration
    config.allowed_origins = [newOrigin];
    
    fs.writeFileSync(hostConfigPath, JSON.stringify(config, null, 2));
    console.log('✅ Updated native messaging host configuration');
    console.log(`   Extension ID: ${extensionId}`);
    
    return true;
  } catch (error) {
    console.error('❌ Error updating native messaging host configuration:', error.message);
    return false;
  }
}

function main() {
  console.log('🔧 Fixing Chrome extension ID mismatch...\n');
  
  // Extract public key from manifest
  const publicKey = extractPublicKeyFromManifest();
  if (!publicKey) {
    console.log('❌ Failed to extract public key from manifest.json');
    return;
  }
  
  // Calculate extension ID
  const extensionId = calculateExtensionId(publicKey);
  console.log(`📋 Calculated extension ID: ${extensionId}`);
  
  // Update native messaging host configuration
  const success = updateNativeMessagingHost(extensionId);
  
  if (success) {
    console.log('\n🎉 Extension ID fix completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Reload the Chrome extension');
    console.log('2. Test the MCP server connection');
  } else {
    console.log('\n❌ Failed to fix extension ID');
    console.log('You may need to manually update the native messaging host configuration');
  }
}

if (require.main === module) {
  main();
}
