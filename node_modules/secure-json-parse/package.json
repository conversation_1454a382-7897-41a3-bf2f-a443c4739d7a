{"name": "secure-json-parse", "version": "4.0.0", "description": "JSON parse with prototype poisoning protection", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"benchmark": "cd benchmarks && npm install && npm run all", "lint": "eslint", "lint:fix": "eslint --fix", "test": "nyc npm run test:unit && npm run test:typescript", "test:unit": "tape \"test/*.test.js\"", "test:typescript": "tsd", "test:browser": "airtap test/*.test.js"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/secure-json-parse.git"}, "author": "<PERSON>n <PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "http://delved.org"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "keywords": ["JSON", "parse", "safe", "security", "prototype", "pollution"], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/fastify/secure-json-parse/issues"}, "homepage": "https://github.com/fastify/secure-json-parse#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "devDependencies": {"@fastify/pre-commit": "^2.1.0", "airtap": "^5.0.0", "airtap-playwright": "^1.0.1", "eslint": "^9.17.0", "neostandard": "^0.12.0", "nyc": "^17.0.0", "playwright": "^1.43.1", "tape": "^5.7.5", "tsd": "^0.31.0"}}