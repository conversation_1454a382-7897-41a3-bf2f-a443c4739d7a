{"version": 3, "file": "stdio-server.js", "sourceRoot": "", "sources": ["../src/stdio-server.ts"], "names": [], "mappings": ";;;AAGA,wEAAiF;AAMjF,wDAAwD;AACxD,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,0CAA0C,CAAC,CAAC;AAE7E;;;GAGG;AACH,MAAM,oBAAoB;IAGxB;QACE,uCAAuC;QACvC,IAAI,CAAC,MAAM,GAAG,YAAY,EAAE,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,SAAS,GAAG,IAAI,+BAAoB,EAAE,CAAC;YAE7C,8BAA8B;YAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAErC,uEAAuE;QACzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gDAAgD;YAChD,OAAO,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,2BAA2B,EACtD,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,wCAAwC,KAAK,IAAI,CAAC,CAAC;YAChF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gCAAgC;YAChC,OAAO,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,2BAA2B,EACtD,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,4BAA4B,KAAK,IAAI,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;CACF;AAED,yBAAyB;AACzB,MAAM,MAAM,GAAG,IAAI,oBAAoB,EAAE,CAAC;AAE1C,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;IAC9B,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;IACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,IAAI,EAAE;IAC/B,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;IACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,mBAAmB;AACnB,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IAC7B,OAAO,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,2BAA2B,EACtD,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,6BAA6B,KAAK,IAAI,CAAC,CAAC;IACrE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}