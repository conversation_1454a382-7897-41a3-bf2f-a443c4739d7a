"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChromeExtensionBridge = void 0;
const child_process_1 = require("child_process");
const events_1 = require("events");
/**
 * Bridge to communicate with Chrome extension via native messaging host
 */
class ChromeExtensionBridge extends events_1.EventEmitter {
    constructor() {
        super();
        this.nativeHostProcess = null;
        this.isConnected = false;
        this.pendingRequests = new Map();
        this.requestIdCounter = 0;
    }
    async initialize() {
        try {
            // Start the native messaging host process
            await this.startNativeHost();
            console.error('Chrome extension bridge initialized');
        }
        catch (error) {
            console.error('Failed to initialize Chrome extension bridge:', error);
            throw error;
        }
    }
    async startNativeHost() {
        return new Promise((resolve, reject) => {
            try {
                // Start the native messaging host using the mcp-chrome-bridge package
                this.nativeHostProcess = (0, child_process_1.spawn)('node', [
                    require.resolve('mcp-chrome-bridge/dist/index.js')
                ], {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    env: { ...process.env }
                });
                if (!this.nativeHostProcess.stdout || !this.nativeHostProcess.stdin) {
                    throw new Error('Failed to create native host process streams');
                }
                // Handle process output
                this.nativeHostProcess.stdout.on('data', (data) => {
                    try {
                        const lines = data.toString().split('\n').filter((line) => line.trim());
                        for (const line of lines) {
                            const message = JSON.parse(line);
                            this.handleNativeHostMessage(message);
                        }
                    }
                    catch (error) {
                        console.error('Error parsing native host message:', error);
                    }
                });
                // Handle process errors
                this.nativeHostProcess.stderr?.on('data', (data) => {
                    console.error('Native host stderr:', data.toString());
                });
                this.nativeHostProcess.on('error', (error) => {
                    console.error('Native host process error:', error);
                    this.isConnected = false;
                    reject(error);
                });
                this.nativeHostProcess.on('exit', (code, signal) => {
                    console.error(`Native host process exited with code ${code}, signal ${signal}`);
                    this.isConnected = false;
                });
                // Wait a moment for the process to start
                setTimeout(() => {
                    this.isConnected = true;
                    resolve();
                }, 1000);
            }
            catch (error) {
                reject(error);
            }
        });
    }
    handleNativeHostMessage(message) {
        if (message.responseToRequestId) {
            // This is a response to a previous request
            const requestId = message.responseToRequestId;
            const pendingRequest = this.pendingRequests.get(requestId);
            if (pendingRequest) {
                clearTimeout(pendingRequest.timeout);
                this.pendingRequests.delete(requestId);
                if (message.payload && message.payload.status === 'success') {
                    pendingRequest.resolve(message.payload.data);
                }
                else {
                    pendingRequest.reject(new Error(message.payload?.error || 'Unknown error'));
                }
            }
        }
    }
    async ensureConnection() {
        if (!this.isConnected) {
            throw new Error('Chrome extension bridge is not connected');
        }
    }
    async callTool(toolName, args) {
        await this.ensureConnection();
        return new Promise((resolve, reject) => {
            const requestId = `req_${++this.requestIdCounter}_${Date.now()}`;
            // Set up timeout
            const timeout = setTimeout(() => {
                this.pendingRequests.delete(requestId);
                reject(new Error(`Tool call timeout: ${toolName}`));
            }, 30000); // 30 second timeout
            // Store the pending request
            this.pendingRequests.set(requestId, { resolve, reject, timeout });
            // Send the request to native host
            const request = {
                type: 'call_tool',
                requestId,
                payload: {
                    name: toolName,
                    args
                }
            };
            try {
                if (this.nativeHostProcess && this.nativeHostProcess.stdin) {
                    this.nativeHostProcess.stdin.write(JSON.stringify(request) + '\n');
                }
                else {
                    throw new Error('Native host process not available');
                }
            }
            catch (error) {
                clearTimeout(timeout);
                this.pendingRequests.delete(requestId);
                reject(error);
            }
        });
    }
    async disconnect() {
        // Clean up pending requests
        for (const [requestId, request] of this.pendingRequests) {
            clearTimeout(request.timeout);
            request.reject(new Error('Connection closed'));
        }
        this.pendingRequests.clear();
        // Close native host process
        if (this.nativeHostProcess) {
            this.nativeHostProcess.kill();
            this.nativeHostProcess = null;
        }
        this.isConnected = false;
        console.error('Chrome extension bridge disconnected');
    }
}
exports.ChromeExtensionBridge = ChromeExtensionBridge;
//# sourceMappingURL=chrome-extension-bridge.js.map