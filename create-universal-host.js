#!/usr/bin/env node

/**
 * Create a universal native messaging host that accepts any extension
 */

const fs = require('fs');
const path = require('path');

function createUniversalHost() {
  console.log('🔧 Creating universal native messaging host...\n');
  
  // Create a host config that accepts any chrome-extension origin
  const hostConfig = {
    name: "com.chromemcp.nativehost",
    description: "Chrome MCP Server Native Host (Universal)",
    path: path.resolve(__dirname, "run_native_host.sh"),
    type: "stdio",
    allowed_origins: [
      "chrome-extension://jcwgqphqhmrdkeiq/",  // Calculated ID
      "chrome-extension://*/",                   // Wildcard (not officially supported but sometimes works)
    ]
  };
  
  // Also try creating multiple specific entries for common developer mode patterns
  const devModePatterns = [
    'abcdefghijklmnopqrstuvwxyz123456',
    'bcdefghijklmnopqrstuvwxyz1234567',
    'cdefghijklmnopqrstuvwxyz12345678',
    'defghijklmnopqrstuvwxyz123456789'
  ];
  
  devModePatterns.forEach(pattern => {
    hostConfig.allowed_origins.push(`chrome-extension://${pattern}/`);
  });
  
  const configDir = path.join(process.env.HOME, 'Library/Application Support/Google/Chrome/NativeMessagingHosts');
  const configPath = path.join(configDir, 'com.chromemcp.nativehost.json');
  
  // Ensure directory exists
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
  }
  
  fs.writeFileSync(configPath, JSON.stringify(hostConfig, null, 2));
  console.log(`✅ Created universal native host configuration: ${configPath}`);
  
  console.log('\n📋 Configuration:');
  console.log(JSON.stringify(hostConfig, null, 2));
  
  console.log('\n⚠️  Note: This is a temporary solution. For production use, please provide the exact extension ID.');
}

if (require.main === module) {
  createUniversalHost();
}
