#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function setupTestConfig() {
  console.log('🧪 Setting up test native messaging config...\n');
  
  const hostConfig = {
    name: "com.chromemcp.nativehost",
    description: "Chrome MCP Server Native Host (Test)",
    path: path.resolve(__dirname, "test-native-messaging.js"),
    type: "stdio",
    allowed_origins: [
      "chrome-extension://hbdgbgagpkpjffpklnamcljpakneikee/"
    ]
  };
  
  const configDir = path.join(process.env.HOME, 'Library/Application Support/Google/Chrome/NativeMessagingHosts');
  const configPath = path.join(configDir, 'com.chromemcp.nativehost.json');
  
  fs.writeFileSync(configPath, JSON.stringify(hostConfig, null, 2));
  console.log(`✅ Updated test configuration: ${configPath}`);
  
  console.log('\n📋 Test configuration:');
  console.log(JSON.stringify(hostConfig, null, 2));
  
  console.log('\n📝 Test log will be written to: /tmp/chrome-mcp-test.log');
  console.log('You can monitor it with: tail -f /tmp/chrome-mcp-test.log');
  
  // Clear old logs
  try {
    fs.unlinkSync('/tmp/chrome-mcp-test.log');
  } catch (e) {
    // File doesn't exist, that's fine
  }
}

if (require.main === module) {
  setupTestConfig();
}
