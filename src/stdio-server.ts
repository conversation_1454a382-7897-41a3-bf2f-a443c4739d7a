#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

// Import the existing MCP server from mcp-chrome-bridge
const { getMcpServer } = require('mcp-chrome-bridge/dist/mcp/mcp-server.js');

/**
 * Chrome MCP Server with stdio transport for Augment compatibility
 * This creates a stdio wrapper around the existing HTTP-based MCP server
 */
class ChromeMcpStdioServer {
  private server: Server;

  constructor() {
    // Get the existing MCP server instance
    this.server = getMcpServer();
  }

  async start(): Promise<void> {
    try {
      // Create stdio transport
      const transport = new StdioServerTransport();

      // Connect server to transport
      await this.server.connect(transport);

      // Don't log to stderr in native messaging - it can cause disconnection
    } catch (error) {
      // Log to a file instead of stderr for debugging
      require('fs').appendFileSync('/tmp/chrome-mcp-error.log',
        `${new Date().toISOString()}: Failed to start Chrome MCP Server: ${error}\n`);
      process.exit(1);
    }
  }

  async stop(): Promise<void> {
    try {
      await this.server.close();
    } catch (error) {
      // Log to file instead of stderr
      require('fs').appendFileSync('/tmp/chrome-mcp-error.log',
        `${new Date().toISOString()}: Error stopping server: ${error}\n`);
    }
  }
}

// Handle process signals
const server = new ChromeMcpStdioServer();

process.on('SIGINT', async () => {
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await server.stop();
  process.exit(0);
});

// Start the server
server.start().catch((error) => {
  require('fs').appendFileSync('/tmp/chrome-mcp-error.log',
    `${new Date().toISOString()}: Failed to start server: ${error}\n`);
  process.exit(1);
});
