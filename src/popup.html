<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome MCP Server</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        width: 400px;
        min-height: 500px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: #f8fafc;
      }

      .container {
        padding: 20px;
      }

      .header {
        text-align: center;
        margin-bottom: 24px;
      }

      .header h1 {
        font-size: 20px;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 8px;
      }

      .header p {
        font-size: 14px;
        color: #64748b;
      }

      .section {
        background: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 12px;
      }

      .interface-selector {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
      }

      .interface-option {
        flex: 1;
        padding: 12px;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        background: white;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
      }

      .interface-option.active {
        border-color: #3b82f6;
        background: #eff6ff;
      }

      .interface-option:hover {
        border-color: #cbd5e1;
      }

      .interface-option .title {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 4px;
      }

      .interface-option .description {
        font-size: 12px;
        color: #64748b;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
      }

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }

      .status-dot.connected {
        background: #10b981;
      }

      .status-dot.disconnected {
        background: #ef4444;
      }

      .status-dot.unknown {
        background: #f59e0b;
      }

      .config-section {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 12px;
        margin-top: 12px;
      }

      .config-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 8px;
      }

      .config-title {
        font-size: 14px;
        font-weight: 500;
        color: #374151;
      }

      .copy-button {
        background: #3b82f6;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: background 0.2s ease;
      }

      .copy-button:hover {
        background: #2563eb;
      }

      .copy-button.copied {
        background: #10b981;
      }

      .config-content {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        padding: 12px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 11px;
        line-height: 1.4;
        color: #374151;
        white-space: pre;
        overflow-x: auto;
      }

      .port-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        font-size: 14px;
        margin-top: 8px;
      }

      .port-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .connect-button {
        width: 100%;
        padding: 10px;
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        margin-top: 12px;
        transition: background 0.2s ease;
      }

      .connect-button:hover {
        background: #2563eb;
      }

      .connect-button:disabled {
        background: #9ca3af;
        cursor: not-allowed;
      }

      .info-text {
        font-size: 12px;
        color: #64748b;
        margin-top: 8px;
        line-height: 1.4;
      }

      .warning {
        background: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 6px;
        padding: 8px;
        margin-top: 8px;
      }

      .warning-text {
        font-size: 12px;
        color: #92400e;
      }

      /* 模型选择器样式 */
      .model-selector {
        margin-bottom: 12px;
      }

      .model-select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        font-size: 14px;
        margin-top: 8px;
        background: white;
      }

      .model-select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .model-status {
        margin-bottom: 12px;
      }

      .model-progress {
        margin-bottom: 12px;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: #e2e8f0;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 4px;
      }

      .progress-fill {
        height: 100%;
        background: #3b82f6;
        transition: width 0.3s ease;
        width: 0%;
      }

      .progress-text {
        font-size: 12px;
        color: #64748b;
        text-align: center;
      }

      .model-button {
        width: 100%;
        padding: 10px;
        background: #10b981;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        margin-bottom: 12px;
        transition: background 0.2s ease;
      }

      .model-button:hover {
        background: #059669;
      }

      .model-button:disabled {
        background: #9ca3af;
        cursor: not-allowed;
      }

      /* 存储统计样式 */
      .storage-stats {
        margin-bottom: 12px;
      }

      .stats-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f1f5f9;
      }

      .stats-item:last-child {
        border-bottom: none;
      }

      .stats-label {
        font-size: 14px;
        color: #64748b;
      }

      .stats-value {
        font-size: 14px;
        font-weight: 500;
        color: #1e293b;
      }

      .storage-actions {
        display: flex;
        gap: 8px;
      }

      .action-button {
        flex: 1;
        padding: 8px 12px;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .action-button.refresh {
        background: #f3f4f6;
        color: #374151;
      }

      .action-button.refresh:hover {
        background: #e5e7eb;
      }

      .action-button.clear {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
      }

      .action-button.clear:hover {
        background: #fee2e2;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Chrome MCP Server</h1>
        <p>为AI助手提供浏览器控制能力</p>
      </div>

      <!-- 接口选择部分 -->
      <div class="section">
        <div class="section-title">🔧 接口类型选择</div>
        <div class="interface-selector">
          <div class="interface-option active" data-type="stdio">
            <div class="title">stdio</div>
            <div class="description">命令行接口<br>适用于Augment</div>
          </div>
          <div class="interface-option" data-type="http">
            <div class="title">HTTP</div>
            <div class="description">HTTP接口<br>适用于其他客户端</div>
          </div>
        </div>

        <div class="config-section" id="configSection">
          <div class="config-header">
            <span class="config-title">MCP配置</span>
            <button class="copy-button" id="copyButton">复制配置</button>
          </div>
          <div class="config-content" id="configContent"></div>
        </div>

        <div class="warning" id="stdioWarning" style="display: none;">
          <div class="warning-text">
            <strong>注意：</strong>stdio模式需要确保Chrome扩展已安装并且native messaging host正在运行。
          </div>
        </div>
      </div>

      <!-- 连接状态部分 -->
      <div class="section">
        <div class="section-title">📡 连接状态</div>
        <div class="status-indicator">
          <div class="status-dot unknown" id="statusDot"></div>
          <span id="statusText">检查连接状态...</span>
        </div>

        <label for="port" style="font-size: 14px; color: #374151;">连接端口</label>
        <input type="number" id="port" class="port-input" value="12306" min="1" max="65535">

        <button class="connect-button" id="connectButton">连接测试</button>
      </div>

      <!-- 语义模型部分 -->
      <div class="section">
        <div class="section-title">🧠 语义搜索引擎</div>
        <div class="model-selector">
          <label for="modelSelect" style="font-size: 14px; color: #374151;">选择模型</label>
          <select id="modelSelect" class="model-select">
            <option value="multilingual-e5-small">multilingual-e5-small (推荐)</option>
            <option value="bge-small-en-v1.5">bge-small-en-v1.5</option>
            <option value="all-MiniLM-L6-v2">all-MiniLM-L6-v2</option>
          </select>
        </div>

        <div class="model-status" id="modelStatus">
          <div class="status-indicator">
            <div class="status-dot unknown" id="modelStatusDot"></div>
            <span id="modelStatusText">检查模型状态...</span>
          </div>
        </div>

        <div class="model-progress" id="modelProgress" style="display: none;">
          <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
          </div>
          <div class="progress-text" id="progressText">0%</div>
        </div>

        <button class="model-button" id="modelButton">初始化模型</button>

        <div class="info-text">
          语义搜索引擎用于智能搜索网页内容，提供更准确的搜索结果。
        </div>
      </div>

      <!-- 存储管理部分 -->
      <div class="section">
        <div class="section-title">💾 存储管理</div>
        <div class="storage-stats" id="storageStats">
          <div class="stats-item">
            <span class="stats-label">缓存大小:</span>
            <span class="stats-value" id="cacheSize">计算中...</span>
          </div>
          <div class="stats-item">
            <span class="stats-label">索引页面:</span>
            <span class="stats-value" id="indexedPages">计算中...</span>
          </div>
        </div>

        <div class="storage-actions">
          <button class="action-button refresh" id="refreshStats">刷新统计</button>
          <button class="action-button clear" id="clearData">清空数据</button>
        </div>
      </div>
    </div>

    <script src="popup.js"></script>
  </body>
</html>
