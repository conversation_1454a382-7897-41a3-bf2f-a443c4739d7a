<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome MCP Server</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        width: 400px;
        min-height: 500px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: #f8fafc;
      }

      .container {
        padding: 20px;
      }

      .header {
        text-align: center;
        margin-bottom: 24px;
      }

      .header h1 {
        font-size: 20px;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 8px;
      }

      .header p {
        font-size: 14px;
        color: #64748b;
      }

      .section {
        background: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 12px;
      }

      .interface-selector {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
      }

      .interface-option {
        flex: 1;
        padding: 12px;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        background: white;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
      }

      .interface-option.active {
        border-color: #3b82f6;
        background: #eff6ff;
      }

      .interface-option:hover {
        border-color: #cbd5e1;
      }

      .interface-option .title {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 4px;
      }

      .interface-option .description {
        font-size: 12px;
        color: #64748b;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
      }

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }

      .status-dot.connected {
        background: #10b981;
      }

      .status-dot.disconnected {
        background: #ef4444;
      }

      .status-dot.unknown {
        background: #f59e0b;
      }

      .config-section {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 12px;
        margin-top: 12px;
      }

      .config-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 8px;
      }

      .config-title {
        font-size: 14px;
        font-weight: 500;
        color: #374151;
      }

      .copy-button {
        background: #3b82f6;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: background 0.2s ease;
      }

      .copy-button:hover {
        background: #2563eb;
      }

      .copy-button.copied {
        background: #10b981;
      }

      .config-content {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        padding: 12px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 11px;
        line-height: 1.4;
        color: #374151;
        white-space: pre;
        overflow-x: auto;
      }

      .port-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        font-size: 14px;
        margin-top: 8px;
      }

      .port-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .connect-button {
        width: 100%;
        padding: 10px;
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        margin-top: 12px;
        transition: background 0.2s ease;
      }

      .connect-button:hover {
        background: #2563eb;
      }

      .connect-button:disabled {
        background: #9ca3af;
        cursor: not-allowed;
      }

      .info-text {
        font-size: 12px;
        color: #64748b;
        margin-top: 8px;
        line-height: 1.4;
      }

      .warning {
        background: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 6px;
        padding: 8px;
        margin-top: 8px;
      }

      .warning-text {
        font-size: 12px;
        color: #92400e;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Chrome MCP Server</h1>
        <p>为AI助手提供浏览器控制能力</p>
      </div>

      <div class="section">
        <div class="section-title">接口类型选择</div>
        <div class="interface-selector">
          <div class="interface-option active" data-type="stdio">
            <div class="title">stdio</div>
            <div class="description">命令行接口<br>适用于Augment</div>
          </div>
          <div class="interface-option" data-type="http">
            <div class="title">HTTP</div>
            <div class="description">HTTP接口<br>适用于其他客户端</div>
          </div>
        </div>

        <div class="status-indicator">
          <div class="status-dot unknown" id="statusDot"></div>
          <span id="statusText">检查连接状态...</span>
        </div>

        <div class="config-section" id="configSection">
          <div class="config-header">
            <span class="config-title">MCP配置</span>
            <button class="copy-button" id="copyButton">复制配置</button>
          </div>
          <div class="config-content" id="configContent"></div>
        </div>

        <label for="port" style="font-size: 14px; color: #374151;">连接端口</label>
        <input type="number" id="port" class="port-input" value="12306" min="1" max="65535">

        <button class="connect-button" id="connectButton">连接测试</button>

        <div class="info-text">
          <strong>stdio模式：</strong>适用于Augment等支持命令行接口的AI助手<br>
          <strong>HTTP模式：</strong>适用于支持HTTP接口的其他客户端
        </div>

        <div class="warning" id="stdioWarning" style="display: none;">
          <div class="warning-text">
            <strong>注意：</strong>stdio模式需要确保Chrome扩展已安装并且native messaging host正在运行。
          </div>
        </div>
      </div>
    </div>

    <script src="popup.js"></script>
  </body>
</html>
