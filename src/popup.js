// Chrome MCP Server Popup Script

class ChromeMcpPopup {
  constructor() {
    this.interfaceType = 'stdio'; // 默认使用stdio
    this.port = 12306;
    this.isConnected = false;
    this.isConnecting = false;

    // 语义模型相关状态
    this.currentModel = 'multilingual-e5-small';
    this.modelStatus = 'idle'; // idle, downloading, ready, error
    this.downloadProgress = 0;
    this.isModelInitializing = false;

    // 存储统计
    this.storageStats = {
      cacheSize: 0,
      indexedPages: 0
    };

    this.init();
  }

  async init() {
    this.bindEvents();
    await this.loadPreferences();
    this.updateInterface();
    this.checkConnection();
    this.checkModelStatus();
    this.loadStorageStats();
  }

  bindEvents() {
    // 接口类型选择
    document.querySelectorAll('.interface-option').forEach(option => {
      option.addEventListener('click', (e) => {
        const type = e.currentTarget.dataset.type;
        this.selectInterface(type);
      });
    });

    // 端口输入
    document.getElementById('port').addEventListener('input', (e) => {
      this.port = parseInt(e.target.value) || 12306;
      this.savePreferences();
      this.updateConfig();
    });

    // 连接按钮
    document.getElementById('connectButton').addEventListener('click', () => {
      this.testConnection();
    });

    // 复制按钮
    document.getElementById('copyButton').addEventListener('click', () => {
      this.copyConfig();
    });

    // 模型选择
    document.getElementById('modelSelect').addEventListener('change', (e) => {
      this.currentModel = e.target.value;
      this.savePreferences();
    });

    // 模型按钮
    document.getElementById('modelButton').addEventListener('click', () => {
      this.initializeModel();
    });

    // 刷新统计
    document.getElementById('refreshStats').addEventListener('click', () => {
      this.loadStorageStats();
    });

    // 清空数据
    document.getElementById('clearData').addEventListener('click', () => {
      this.clearAllData();
    });
  }

  selectInterface(type) {
    this.interfaceType = type;
    
    // 更新UI
    document.querySelectorAll('.interface-option').forEach(option => {
      option.classList.remove('active');
    });
    document.querySelector(`[data-type="${type}"]`).classList.add('active');
    
    // 显示/隐藏警告
    const warning = document.getElementById('stdioWarning');
    warning.style.display = type === 'stdio' ? 'block' : 'none';
    
    this.savePreferences();
    this.updateConfig();
  }

  updateConfig() {
    const configContent = document.getElementById('configContent');
    let config;

    if (this.interfaceType === 'stdio') {
      // stdio配置 - 显示Augment设置格式
      config = `Augment MCP设置：

Name（名称）：
chrome-mcp-server

Command（命令）：
/Users/<USER>/Documents/mcp/chrome-mcp-server-0.0.3/dist/stdio-server.js

Environment Variables（环境变量）：
（留空，不需要添加）

---

或者使用JSON配置文件格式：
{
  "mcpServers": {
    "chrome-mcp-server": {
      "command": "/Users/<USER>/Documents/mcp/chrome-mcp-server-0.0.3/dist/stdio-server.js",
      "args": []
    }
  }
}`;
    } else {
      // HTTP配置
      const httpConfig = {
        mcpServers: {
          "chrome-mcp-server": {
            type: "streamable-http",
            url: `http://127.0.0.1:${this.port}/sse`
          }
        }
      };
      config = JSON.stringify(httpConfig, null, 2);
    }

    configContent.textContent = config;
  }

  async copyConfig() {
    const configContent = document.getElementById('configContent').textContent;
    const copyButton = document.getElementById('copyButton');
    
    try {
      await navigator.clipboard.writeText(configContent);
      copyButton.textContent = '✅ 已复制';
      copyButton.classList.add('copied');
      
      setTimeout(() => {
        copyButton.textContent = '复制配置';
        copyButton.classList.remove('copied');
      }, 2000);
    } catch (error) {
      console.error('复制失败:', error);
      copyButton.textContent = '❌ 复制失败';
      
      setTimeout(() => {
        copyButton.textContent = '复制配置';
      }, 2000);
    }
  }

  async testConnection() {
    if (this.isConnecting) return;
    
    this.isConnecting = true;
    const connectButton = document.getElementById('connectButton');
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    
    connectButton.textContent = '连接中...';
    connectButton.disabled = true;
    statusText.textContent = '正在测试连接...';
    
    try {
      if (this.interfaceType === 'stdio') {
        // 测试stdio连接（通过native messaging）
        const response = await chrome.runtime.sendMessage({ 
          type: 'ping_native' 
        });
        
        this.isConnected = response?.connected || false;
      } else {
        // 测试HTTP连接
        const response = await fetch(`http://127.0.0.1:${this.port}/ask-extension`);
        this.isConnected = response.ok;
      }
      
      this.updateConnectionStatus();
      
    } catch (error) {
      console.error('连接测试失败:', error);
      this.isConnected = false;
      this.updateConnectionStatus();
    } finally {
      this.isConnecting = false;
      connectButton.textContent = '连接测试';
      connectButton.disabled = false;
    }
  }

  async checkConnection() {
    try {
      if (this.interfaceType === 'stdio') {
        const response = await chrome.runtime.sendMessage({ 
          type: 'ping_native' 
        });
        this.isConnected = response?.connected || false;
      } else {
        const response = await fetch(`http://127.0.0.1:${this.port}/ask-extension`);
        this.isConnected = response.ok;
      }
    } catch (error) {
      this.isConnected = false;
    }
    
    this.updateConnectionStatus();
  }

  updateConnectionStatus() {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    
    statusDot.className = 'status-dot';
    
    if (this.isConnected) {
      statusDot.classList.add('connected');
      statusText.textContent = `${this.interfaceType.toUpperCase()} 连接正常`;
    } else {
      statusDot.classList.add('disconnected');
      statusText.textContent = `${this.interfaceType.toUpperCase()} 连接失败`;
    }
  }

  updateInterface() {
    // 更新端口输入
    document.getElementById('port').value = this.port;

    // 更新接口选择
    document.querySelectorAll('.interface-option').forEach(option => {
      option.classList.remove('active');
    });
    document.querySelector(`[data-type="${this.interfaceType}"]`).classList.add('active');

    // 显示/隐藏警告
    const warning = document.getElementById('stdioWarning');
    warning.style.display = this.interfaceType === 'stdio' ? 'block' : 'none';

    // 更新模型选择
    document.getElementById('modelSelect').value = this.currentModel;

    this.updateConfig();
    this.updateModelInterface();
    this.updateStorageInterface();
  }

  updateModelInterface() {
    const modelStatusDot = document.getElementById('modelStatusDot');
    const modelStatusText = document.getElementById('modelStatusText');
    const modelProgress = document.getElementById('modelProgress');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const modelButton = document.getElementById('modelButton');

    // 更新状态点
    modelStatusDot.className = 'status-dot';

    switch (this.modelStatus) {
      case 'ready':
        modelStatusDot.classList.add('connected');
        modelStatusText.textContent = '模型已就绪';
        modelProgress.style.display = 'none';
        modelButton.textContent = '重新初始化';
        modelButton.disabled = false;
        break;
      case 'downloading':
        modelStatusDot.classList.add('unknown');
        modelStatusText.textContent = '模型下载中...';
        modelProgress.style.display = 'block';
        progressFill.style.width = `${this.downloadProgress}%`;
        progressText.textContent = `${this.downloadProgress}%`;
        modelButton.textContent = '下载中...';
        modelButton.disabled = true;
        break;
      case 'error':
        modelStatusDot.classList.add('disconnected');
        modelStatusText.textContent = '模型初始化失败';
        modelProgress.style.display = 'none';
        modelButton.textContent = '重试';
        modelButton.disabled = false;
        break;
      default:
        modelStatusDot.classList.add('unknown');
        modelStatusText.textContent = '模型未初始化';
        modelProgress.style.display = 'none';
        modelButton.textContent = '初始化模型';
        modelButton.disabled = false;
    }
  }

  updateStorageInterface() {
    const cacheSize = document.getElementById('cacheSize');
    const indexedPages = document.getElementById('indexedPages');

    // 格式化缓存大小
    const formatSize = (bytes) => {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    cacheSize.textContent = formatSize(this.storageStats.cacheSize);
    indexedPages.textContent = this.storageStats.indexedPages.toString();
  }

  async savePreferences() {
    try {
      await chrome.storage.local.set({
        interfaceType: this.interfaceType,
        port: this.port
      });
    } catch (error) {
      console.error('保存偏好设置失败:', error);
    }
  }

  async loadPreferences() {
    try {
      const result = await chrome.storage.local.get([
        'interfaceType',
        'port',
        'selectedModel',
        'modelState',
        'semanticEngineState'
      ]);

      if (result.interfaceType) {
        this.interfaceType = result.interfaceType;
      }

      if (result.port) {
        this.port = result.port;
      }

      if (result.selectedModel) {
        this.currentModel = result.selectedModel;
      }

      if (result.modelState) {
        this.modelStatus = result.modelState.status || 'idle';
        this.downloadProgress = result.modelState.downloadProgress || 0;
      }
    } catch (error) {
      console.error('加载偏好设置失败:', error);
    }
  }

  async checkModelStatus() {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'get_model_status'
      });

      if (response) {
        this.modelStatus = response.status || 'idle';
        this.downloadProgress = response.progress || 0;
        this.updateModelInterface();
      }
    } catch (error) {
      console.error('检查模型状态失败:', error);
      this.modelStatus = 'idle';
      this.updateModelInterface();
    }
  }

  async initializeModel() {
    if (this.isModelInitializing) return;

    this.isModelInitializing = true;
    this.modelStatus = 'downloading';
    this.downloadProgress = 0;

    const modelButton = document.getElementById('modelButton');
    modelButton.textContent = '初始化中...';
    modelButton.disabled = true;

    this.updateModelInterface();

    try {
      const response = await chrome.runtime.sendMessage({
        type: 'switch_semantic_model',
        modelPreset: this.currentModel,
        modelVersion: 'quantized'
      });

      if (response && response.success) {
        this.modelStatus = 'ready';
        this.downloadProgress = 100;
        await this.saveModelState();
      } else {
        throw new Error(response?.error || '模型初始化失败');
      }
    } catch (error) {
      console.error('模型初始化失败:', error);
      this.modelStatus = 'error';
    } finally {
      this.isModelInitializing = false;
      this.updateModelInterface();
    }
  }

  async loadStorageStats() {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'get_storage_stats'
      });

      if (response) {
        this.storageStats = {
          cacheSize: response.cacheSize || 0,
          indexedPages: response.indexedPages || 0
        };
        this.updateStorageInterface();
      }
    } catch (error) {
      console.error('加载存储统计失败:', error);
    }
  }

  async clearAllData() {
    if (!confirm('确定要清空所有数据吗？此操作不可撤销！')) {
      return;
    }

    try {
      const response = await chrome.runtime.sendMessage({
        type: 'clear_all_data'
      });

      if (response && response.success) {
        this.storageStats = { cacheSize: 0, indexedPages: 0 };
        this.updateStorageInterface();
        alert('数据清空成功！');
      } else {
        throw new Error(response?.error || '清空数据失败');
      }
    } catch (error) {
      console.error('清空数据失败:', error);
      alert('清空数据失败: ' + error.message);
    }
  }

  async saveModelState() {
    try {
      await chrome.storage.local.set({
        selectedModel: this.currentModel,
        modelState: {
          status: this.modelStatus,
          downloadProgress: this.downloadProgress,
          lastUpdated: Date.now()
        }
      });
    } catch (error) {
      console.error('保存模型状态失败:', error);
    }
  }
}

// 初始化popup
document.addEventListener('DOMContentLoaded', () => {
  new ChromeMcpPopup();
});
