// Chrome MCP Server Popup Script

class ChromeMcpPopup {
  constructor() {
    this.interfaceType = 'stdio'; // 默认使用stdio
    this.port = 12306;
    this.isConnected = false;
    this.isConnecting = false;
    
    this.init();
  }

  async init() {
    this.bindEvents();
    await this.loadPreferences();
    this.updateInterface();
    this.checkConnection();
  }

  bindEvents() {
    // 接口类型选择
    document.querySelectorAll('.interface-option').forEach(option => {
      option.addEventListener('click', (e) => {
        const type = e.currentTarget.dataset.type;
        this.selectInterface(type);
      });
    });

    // 端口输入
    document.getElementById('port').addEventListener('input', (e) => {
      this.port = parseInt(e.target.value) || 12306;
      this.savePreferences();
      this.updateConfig();
    });

    // 连接按钮
    document.getElementById('connectButton').addEventListener('click', () => {
      this.testConnection();
    });

    // 复制按钮
    document.getElementById('copyButton').addEventListener('click', () => {
      this.copyConfig();
    });
  }

  selectInterface(type) {
    this.interfaceType = type;
    
    // 更新UI
    document.querySelectorAll('.interface-option').forEach(option => {
      option.classList.remove('active');
    });
    document.querySelector(`[data-type="${type}"]`).classList.add('active');
    
    // 显示/隐藏警告
    const warning = document.getElementById('stdioWarning');
    warning.style.display = type === 'stdio' ? 'block' : 'none';
    
    this.savePreferences();
    this.updateConfig();
  }

  updateConfig() {
    const configContent = document.getElementById('configContent');
    let config;

    if (this.interfaceType === 'stdio') {
      // stdio配置
      config = {
        mcpServers: {
          "chrome-mcp-server": {
            command: "node",
            args: [
              "/Users/<USER>/Documents/mcp/chrome-mcp-server-0.0.3/dist/stdio-server.js"
            ]
          }
        }
      };
    } else {
      // HTTP配置
      config = {
        mcpServers: {
          "chrome-mcp-server": {
            type: "streamable-http",
            url: `http://127.0.0.1:${this.port}/sse`
          }
        }
      };
    }

    configContent.textContent = JSON.stringify(config, null, 2);
  }

  async copyConfig() {
    const configContent = document.getElementById('configContent').textContent;
    const copyButton = document.getElementById('copyButton');
    
    try {
      await navigator.clipboard.writeText(configContent);
      copyButton.textContent = '✅ 已复制';
      copyButton.classList.add('copied');
      
      setTimeout(() => {
        copyButton.textContent = '复制配置';
        copyButton.classList.remove('copied');
      }, 2000);
    } catch (error) {
      console.error('复制失败:', error);
      copyButton.textContent = '❌ 复制失败';
      
      setTimeout(() => {
        copyButton.textContent = '复制配置';
      }, 2000);
    }
  }

  async testConnection() {
    if (this.isConnecting) return;
    
    this.isConnecting = true;
    const connectButton = document.getElementById('connectButton');
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    
    connectButton.textContent = '连接中...';
    connectButton.disabled = true;
    statusText.textContent = '正在测试连接...';
    
    try {
      if (this.interfaceType === 'stdio') {
        // 测试stdio连接（通过native messaging）
        const response = await chrome.runtime.sendMessage({ 
          type: 'ping_native' 
        });
        
        this.isConnected = response?.connected || false;
      } else {
        // 测试HTTP连接
        const response = await fetch(`http://127.0.0.1:${this.port}/ask-extension`);
        this.isConnected = response.ok;
      }
      
      this.updateConnectionStatus();
      
    } catch (error) {
      console.error('连接测试失败:', error);
      this.isConnected = false;
      this.updateConnectionStatus();
    } finally {
      this.isConnecting = false;
      connectButton.textContent = '连接测试';
      connectButton.disabled = false;
    }
  }

  async checkConnection() {
    try {
      if (this.interfaceType === 'stdio') {
        const response = await chrome.runtime.sendMessage({ 
          type: 'ping_native' 
        });
        this.isConnected = response?.connected || false;
      } else {
        const response = await fetch(`http://127.0.0.1:${this.port}/ask-extension`);
        this.isConnected = response.ok;
      }
    } catch (error) {
      this.isConnected = false;
    }
    
    this.updateConnectionStatus();
  }

  updateConnectionStatus() {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    
    statusDot.className = 'status-dot';
    
    if (this.isConnected) {
      statusDot.classList.add('connected');
      statusText.textContent = `${this.interfaceType.toUpperCase()} 连接正常`;
    } else {
      statusDot.classList.add('disconnected');
      statusText.textContent = `${this.interfaceType.toUpperCase()} 连接失败`;
    }
  }

  updateInterface() {
    // 更新端口输入
    document.getElementById('port').value = this.port;
    
    // 更新接口选择
    document.querySelectorAll('.interface-option').forEach(option => {
      option.classList.remove('active');
    });
    document.querySelector(`[data-type="${this.interfaceType}"]`).classList.add('active');
    
    // 显示/隐藏警告
    const warning = document.getElementById('stdioWarning');
    warning.style.display = this.interfaceType === 'stdio' ? 'block' : 'none';
    
    this.updateConfig();
  }

  async savePreferences() {
    try {
      await chrome.storage.local.set({
        interfaceType: this.interfaceType,
        port: this.port
      });
    } catch (error) {
      console.error('保存偏好设置失败:', error);
    }
  }

  async loadPreferences() {
    try {
      const result = await chrome.storage.local.get(['interfaceType', 'port']);
      
      if (result.interfaceType) {
        this.interfaceType = result.interfaceType;
      }
      
      if (result.port) {
        this.port = result.port;
      }
    } catch (error) {
      console.error('加载偏好设置失败:', error);
    }
  }
}

// 初始化popup
document.addEventListener('DOMContentLoaded', () => {
  new ChromeMcpPopup();
});
