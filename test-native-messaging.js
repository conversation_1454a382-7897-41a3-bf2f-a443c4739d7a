#!/usr/bin/env node

/**
 * Simple test script for native messaging protocol
 * This helps debug the communication between Chrome and our native host
 */

const fs = require('fs');

function log(message) {
  fs.appendFileSync('/tmp/chrome-mcp-test.log', 
    `${new Date().toISOString()}: ${message}\n`);
}

function readMessage() {
  return new Promise((resolve, reject) => {
    const lengthBuffer = Buffer.alloc(4);
    let bytesRead = 0;
    
    const readLength = () => {
      const chunk = process.stdin.read(4 - bytesRead);
      if (chunk) {
        chunk.copy(lengthBuffer, bytesRead);
        bytesRead += chunk.length;
        
        if (bytesRead === 4) {
          const messageLength = lengthBuffer.readUInt32LE(0);
          log(`Expecting message of length: ${messageLength}`);
          
          if (messageLength > 1024 * 1024) { // 1MB limit
            reject(new Error(`Message too large: ${messageLength}`));
            return;
          }
          
          readMessageContent(messageLength);
        } else {
          process.stdin.once('readable', readLength);
        }
      } else {
        process.stdin.once('readable', readLength);
      }
    };
    
    const readMessageContent = (length) => {
      const messageBuffer = Buffer.alloc(length);
      let contentBytesRead = 0;
      
      const readContent = () => {
        const chunk = process.stdin.read(length - contentBytesRead);
        if (chunk) {
          chunk.copy(messageBuffer, contentBytesRead);
          contentBytesRead += chunk.length;
          
          if (contentBytesRead === length) {
            try {
              const message = JSON.parse(messageBuffer.toString('utf8'));
              log(`Received message: ${JSON.stringify(message)}`);
              resolve(message);
            } catch (error) {
              reject(new Error(`Failed to parse JSON: ${error.message}`));
            }
          } else {
            process.stdin.once('readable', readContent);
          }
        } else {
          process.stdin.once('readable', readContent);
        }
      };
      
      readContent();
    };
    
    readLength();
  });
}

function sendMessage(message) {
  const messageStr = JSON.stringify(message);
  const messageBuffer = Buffer.from(messageStr, 'utf8');
  const lengthBuffer = Buffer.alloc(4);
  lengthBuffer.writeUInt32LE(messageBuffer.length, 0);
  
  log(`Sending message: ${messageStr}`);
  process.stdout.write(lengthBuffer);
  process.stdout.write(messageBuffer);
}

async function main() {
  log('Native messaging test started');
  
  try {
    // Wait for initialization message
    const initMessage = await readMessage();
    
    // Send a simple response
    sendMessage({
      jsonrpc: "2.0",
      id: initMessage.id || 1,
      result: {
        protocolVersion: "2024-11-05",
        capabilities: {
          tools: {}
        },
        serverInfo: {
          name: "chrome-mcp-server-test",
          version: "1.0.0"
        }
      }
    });
    
    log('Initialization response sent');
    
    // Keep the process alive
    setInterval(() => {
      log('Process still alive');
    }, 5000);
    
  } catch (error) {
    log(`Error: ${error.message}`);
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  log('Received SIGINT, exiting');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('Received SIGTERM, exiting');
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  log(`Uncaught exception: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  log(`Unhandled rejection: ${reason}`);
  process.exit(1);
});

main();
